import { useState, useRef } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router";
import { Button, Card, Tabs } from "antd";
import { ChevronRight, ChevronLeft, Plus } from "lucide-react";
import PropTypes from "prop-types";

const ScrollableList = ({ items, type, onAdd }) => {
  const containerRef = useRef(null);
  const navigate = useNavigate();

  const scroll = (direction) => {
    const container = containerRef.current;
    if (container) {
      container.scrollBy({
        left: direction === "left" ? -300 : 300,
        behavior: "smooth",
      });
    }
  };

  const handlePortfolioClick = (portfolioId) => {
    navigate(`/portfolio/${portfolioId}`);
  };

  return (
    <div className="relative flex items-center mb-2">
      {items.length > 0 && (
        <Button
          onClick={() => scroll("left")}
          className="absolute left-0 z-10 bg-white dark:!bg-gray-700 rounded-full shadow-md border border-gray-200 dark:!border-gray-600 flex items-center justify-center min-w-0 w-8 h-8 hover:bg-gray-50 dark:hover:bg-gray-600"
          icon={
            <ChevronLeft
              size={16}
              className="text-gray-600 dark:!text-gray-300"
            />
          }
        />
      )}

      <div
        ref={containerRef}
        className="flex overflow-x-auto gap-3 mx-2 scrollbar-hide py-3"
        style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
      >
        {items.map((item) => (
          <Card
            key={item.id}
            className="min-w-[160px] cursor-pointer border border-gray-200 dark:!border-gray-700 rounded-lg hover:shadow-md transition-all bg-white dark:!bg-gray-800"
            bodyStyle={{ padding: "6px" }}
            onClick={() =>
              type === "portfolio" ? handlePortfolioClick(item.id) : undefined
            }
          >
            <div className="flex items-center">
              <div className="!bg-blue-100 dark:!bg-gray-700 rounded-lg p-2 mr-2 flex items-center justify-center">
                {type === "portfolio" ? (
                  <span className="text-blue-600 dark:!text-blue-300 text-base">
                    📊
                  </span>
                ) : (
                  <span className="text-blue-600 dark:!text-blue-300 text-base">
                    📋
                  </span>
              )}
            </div>
            <div className="flex flex-col">
                <span className="font-medium text-gray-900 dark:text-white text-sm">
                  {item.name}
                </span>
                {/* <span className="text-xs text-gray-500 dark:text-gray-400">
                  {item.count} items
                </span> */}
              </div>
            </div>
          </Card>
        ))}

        <Button
          onClick={onAdd}
          className="flex items-center justify-center min-w-[160px] !h-[50px] border border-dashed !border-gray-300 dark:!border-gray-600 rounded-lg hover:!border-blue-400 hover:!bg-blue-50 dark:hover:!bg-gray-700 hover:!text-blue-500 transition-all"
          type="text"
        >
          <div className="flex gap-2 items-center">
            <Plus
              size={18}
              className="mb-1 text-gray-500 dark:!text-gray-400"
            />
            <span className="text-gray-600 dark:!text-gray-300 text-sm">
          New {type}
            </span>
          </div>
        </Button>
      </div>

      {items.length > 0 && (
        <Button
          onClick={() => scroll("right")}
          className="absolute right-0 z-10 bg-white dark:!bg-gray-700 rounded-full shadow-md border border-gray-200 dark:!border-gray-600 flex items-center justify-center min-w-0 w-8 h-8 hover:bg-gray-50 dark:hover:bg-gray-600"
          icon={
            <ChevronRight
              size={16}
              className="text-gray-600 dark:!text-gray-300"
            />
          }
        />
      )}
    </div>
  );
};

ScrollableList.propTypes = {
  items: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      count: PropTypes.number.isRequired,
    })
  ).isRequired,
  type: PropTypes.oneOf(["watchlist", "portfolio"]).isRequired,
  onAdd: PropTypes.func.isRequired,
};

const WatchlistPortfolioTabs = ({ onCreatePortfolio }) => {
  const [activeTab, setActiveTab] = useState("portfolio");

  const watchlists = useSelector((state) => state.watchlist?.items || []);
  const portfolios = useSelector((state) => state.portfolio?.portfolios || []);

  const formattedWatchlists = watchlists.map((w) => ({
    id: w.id,
    name: w.name,
    count: w.stocks?.length || 0,
  }));

  const formattedPortfolios = portfolios.map((p) => ({
    id: p.id,
    name: p.name,
    count: p.holdings?.length || 0,
  }));

  return (
    <div className="bg-white dark:!bg-gray-800 rounded-lg">
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        className="custom-tabs"
        items={[
          {
            key: "watchlist",
            label: "Watchlists",
            children: (
              <div className="px-4 pb-4">
          <ScrollableList
            items={formattedWatchlists}
            type="watchlist"
            onAdd={() => {
              // Optional: Handle Watchlist add
            }}
          />
              </div>
            ),
          },
          {
            key: "portfolio",
            label: "Portfolios",
            children: (
              <div className="px-4 pb-4">
          <ScrollableList
            items={formattedPortfolios}
            type="portfolio"
            onAdd={() => {
                    if (onCreatePortfolio) {
                      onCreatePortfolio();
                    }
            }}
          />
      </div>
            ),
          },
        ]}
      />
    </div>
  );
};

WatchlistPortfolioTabs.propTypes = {
  onCreatePortfolio: PropTypes.func,
};

WatchlistPortfolioTabs.defaultProps = {
  onCreatePortfolio: () => {},
};

export default WatchlistPortfolioTabs;
