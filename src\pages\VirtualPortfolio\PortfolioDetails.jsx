import React, { useState, useEffect, useMemo } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import {
  setActivePortfolio,
  renamePortfolioAsync,
  deletePortfolioAsync,
  deleteStockFromPortfolio,
  deleteAllHoldingsAsync,
  fetchPortfolioHoldings,
  fetchAllPortfolios,
} from "../../redux/portfolio/portfolioSlice";
import AddInvestmentModal from "./AddInvestmentModal";
import HoldingsTable from "./HoldingsTable";
import PortfolioModal from "./PortfolioModal";
import { Chart, registerables } from "chart.js";
import { Line } from "react-chartjs-2";
import {
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Filler,
  Legend,
} from "chart.js";
import { toast } from "react-toastify";
import { Trash2 } from "lucide-react";

Chart.register(...registerables);

const PortfolioDetails = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isPortfolioModalOpen, setIsPortfolioModalOpen] = useState(false);
  const [gainType, setGainType] = useState("totalGain"); // Default gain type
  const dispatch = useDispatch();

  const { portfolios, holdingsData, loading, error } = useSelector(
    (state) => state.portfolio
  );
  const activePortfolio = portfolios.find((p) => p.id === id);

  // Fetch all portfolios when component mounts
  useEffect(() => {
    dispatch(fetchAllPortfolios());
  }, [dispatch]);

  // State to store both day gain and total gain data
  const [portfolioData, setPortfolioData] = useState({
    totalValue: 0,
    dayGain: 0,
    dayGainPercent: 0,
    totalGain: 0,
    totalGainPercent: 0,
  });

  // State to store the selected stock for the AddInvestmentModal
  const [selectedStock, setSelectedStock] = useState(null);

  // Fetch portfolio holdings when component mounts or when portfolio ID changes
  useEffect(() => {
    if (id) {
      console.log(
        `Fetching holdings for portfolio ${id} with gainType: ${gainType}`
      );
      // Fetch holdings with the current gain type
      dispatch(
        fetchPortfolioHoldings({
          portfolioId: id,
          gainType: gainType || "totalGain",
        })
      );
    }
  }, [dispatch, id, gainType]);

  // Listen for gain type change events from the chart component
  // and for openAddInvestmentModal events from the HoldingsTable
  useEffect(() => {
    const handleGainTypeChangeEvent = (event) => {
      setGainType(event.detail);
    };

    const handleOpenAddInvestmentModal = () => {
      setIsAddModalOpen(true);
    };

    window.addEventListener("gainTypeChange", handleGainTypeChangeEvent);
    window.addEventListener(
      "openAddInvestmentModal",
      handleOpenAddInvestmentModal
    );

    return () => {
      window.removeEventListener("gainTypeChange", handleGainTypeChangeEvent);
      window.removeEventListener(
        "openAddInvestmentModal",
        handleOpenAddInvestmentModal
      );
    };
  }, []);

  // Update portfolio data when holdings data changes
  useEffect(() => {
    if (
      holdingsData &&
      holdingsData.holdings &&
      holdingsData.holdings.length > 0
    ) {
      // Calculate metrics based on current data
      const metrics = calculatePortfolioMetrics();

      // Update portfolio data with all metrics
      console.log("Updating portfolio data with metrics:", metrics);
      setPortfolioData({
        totalValue: metrics.totalValue,
        dayGain: metrics.dayGain,
        dayGainPercent: metrics.dayGainPercent,
        totalGain: metrics.totalGain,
        totalGainPercent: metrics.totalGainPercent,
      });
    }
  }, [holdingsData]);

  // Debug the holdings data when it changes
  useEffect(() => {
    if (holdingsData && holdingsData.holdings) {
      // console.log("Holdings data received:", holdingsData.holdings);

      // Check for any holdings with placeholder names
      const placeholderNames = holdingsData.holdings.filter(
        (holding) => !holding.name || holding.name === holding.symbol
      );

      // if (placeholderNames.length > 0) {
      //   console.warn(
      //     `Found ${placeholderNames.length} holdings with placeholder names`
      //   );
      // }
    }
  }, [holdingsData]);

  useEffect(() => {
    if (activePortfolio) {
      dispatch(setActivePortfolio(activePortfolio.id));
    }
  }, [dispatch, activePortfolio]);

  // Handle portfolio not found
  useEffect(() => {
    if (id && !activePortfolio && portfolios.length > 0) {
      toast.error("Portfolio not found. Redirecting to home page...");
      setTimeout(() => navigate("/virtual-portfolio"), 2000);
    }
  }, [id, activePortfolio, portfolios, navigate]);

  // Handler to rename portfolio
  const handleRenamePortfolio = (newName) => {
    if (!newName || newName.trim() === "") {
      toast.error("Portfolio name cannot be empty");
      return;
    }

    const toastId = toast.loading(`Renaming portfolio to "${newName}"...`);

    dispatch(renamePortfolioAsync({ portfolioId: id, newName }))
      .unwrap()
      .then(() => {
        toast.update(toastId, {
          render: "Portfolio renamed successfully",
          type: "success",
          isLoading: false,
          autoClose: 2000,
        });
      })
      .catch((error) => {
        toast.update(toastId, {
          render: `Failed to rename portfolio: ${error}`,
          type: "error",
          isLoading: false,
          autoClose: 3000,
        });
      });
  };

  // Handler to delete portfolio
  const handleDeletePortfolio = () => {
    // Show confirmation before deleting
    if (
      window.confirm(
        `Are you sure you want to delete "${activePortfolio.name}" portfolio? This action cannot be undone.`
      )
    ) {
      const toastId = toast.loading("Deleting portfolio...");

      dispatch(deletePortfolioAsync(id))
        .unwrap()
        .then(() => {
          toast.update(toastId, {
            render: "Portfolio deleted successfully",
            type: "success",
            isLoading: false,
            autoClose: 2000,
          });
          navigate("/virtual-portfolio"); // Redirect to home or portfolios list after deletion
        })
        .catch((error) => {
          toast.update(toastId, {
            render: `Failed to delete portfolio: ${error}`,
            type: "error",
            isLoading: false,
            autoClose: 3000,
          });
        });
    }
  };

  // Handler to delete a stock from portfolio
  const handleDeleteStock = async (symbol) => {
    const holding = holdingsData?.holdings.find((h) => h.symbol === symbol);
    const stockName = holding?.name || symbol;

    // Show confirmation before deleting
    if (
      window.confirm(
        `Are you sure you want to delete ${stockName} from your portfolio? This action cannot be undone.`
      )
    ) {
      const toastId = toast.loading(`Removing ${stockName} from portfolio...`);

      try {
        // Dispatch the action to delete all holdings for this stock
        const resultAction = await dispatch(
          deleteAllHoldingsAsync({
            portfolioId: id,
            symbol,
          })
        );

        // Check if the action was fulfilled or rejected
        if (deleteAllHoldingsAsync.fulfilled.match(resultAction)) {
          // Success case
          toast.update(toastId, {
            render: `${stockName} removed from portfolio successfully`,
            type: "success",
            isLoading: false,
            autoClose: 2000,
          });

          // Refresh holdings data after deletion
          dispatch(fetchPortfolioHoldings({ portfolioId: id, gainType }));
        } else {
          // Error case
          toast.update(toastId, {
            render:
              resultAction.payload ||
              `Failed to remove ${stockName} from portfolio`,
            type: "error",
            isLoading: false,
            autoClose: 3000,
          });
        }
      } catch (error) {
        console.error("Error deleting stock:", error);
        toast.update(toastId, {
          render: `Failed to remove ${stockName} from portfolio: ${
            error.message || error
          }`,
          type: "error",
          isLoading: false,
          autoClose: 3000,
        });
      }
    }
  };

  // Handler to change gain type
  const handleGainTypeChange = (type) => {
    setGainType(type);
    toast.info(`Viewing ${type === "totalGain" ? "total" : "daily"} gains`, {
      autoClose: 1500,
    });
  };

  // Calculate total portfolio value and gains from API data
  const calculatePortfolioMetrics = () => {
    if (
      !holdingsData ||
      !holdingsData.holdings ||
      holdingsData.holdings.length === 0
    ) {
      return {
        totalValue: 0,
        dayGain: 0,
        dayGainPercent: 0,
        totalGain: 0,
        totalGainPercent: 0,
      };
    }

    const totalValue = holdingsData.holdings.reduce(
      (sum, holding) => sum + holding.value,
      0
    );

    // Calculate day gain values
    let dayGainValue = 0;
    let dayGainPercent = 0;

    // For day gain, use day_gain values if available
    if (holdingsData.holdings[0]?.day_gain !== undefined) {
      dayGainValue = holdingsData.holdings.reduce(
        (sum, holding) => sum + (holding.day_gain || 0),
        0
      );

      // Calculate weighted average day gain percentage
      dayGainPercent = holdingsData.holdings.reduce((sum, holding) => {
        return (
          sum + (holding.day_gain_percent || 0) * (holding.value / totalValue)
        );
      }, 0);
    }

    // Calculate total gain values
    const totalGainValue = holdingsData.holdings.reduce(
      (sum, holding) => sum + (holding.total_gain || 0),
      0
    );

    // Calculate weighted average total gain percentage
    const totalGainPercent = holdingsData.holdings.reduce((sum, holding) => {
      return (
        sum + (holding.total_gain_percent || 0) * (holding.value / totalValue)
      );
    }, 0);

    return {
      totalValue,
      dayGain: dayGainValue,
      dayGainPercent,
      totalGain: totalGainValue,
      totalGainPercent,
    };
  };

  // Portfolio metrics are now calculated in the useEffect and stored in portfolioData state

  if (!activePortfolio) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-500">Portfolio not found</p>
        <button
          onClick={() => navigate("/virtual-portfolio")}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
        >
          Return to Home
        </button>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Portfolio Tabs */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex space-x-2 overflow-x-auto">
          {portfolios.map((portfolio) => (
            <button
              key={portfolio.id}
              onClick={() => navigate(`/portfolio/${portfolio.id}`)}
              className={`px-4 py-2 rounded-lg ${
                id === portfolio.id
                  ? "bg-blue-500 text-white"
                  : "bg-gray-100 text-gray-700 dark:!bg-gray-700 dark:!text-gray-200"
              }`}
            >
              {portfolio.name}
            </button>
          ))}
          <button
            onClick={() => setIsPortfolioModalOpen(true)}
            className="px-4 py-2 font-medium bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            + New Portfolio
          </button>
        </div>

        <button
          onClick={handleDeletePortfolio}
          className="flex items-center px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
          title="Delete Portfolio"
        >
          <Trash2 size={18} className="mr-1" />
          Delete
        </button>
      </div>

      {/* Add Investment Button */}
      {/* <div className="text-center mb-6">
        <button
          onClick={() => setIsAddModalOpen(true)}
          className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
        >
          + Add Investment
        </button>
      </div> */}

      {/* Main Content - Side by Side Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        {/* Left Column - Chart (Takes 2/3 of space on large screens) */}
        <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-md p-0">
          {loading ? (
            <div className="text-center py-8">
              <div className="inline-block h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              <p className="mt-2 text-gray-600 dark:text-gray-300">
                Loading portfolio data...
              </p>
            </div>
          ) : !loading && !error && holdingsData?.holdings?.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500 dark:text-gray-400">
                No investments in this portfolio yet
              </p>
            </div>
          ) : (
            <PortfolioPerformanceChart
              totalValue={portfolioData.totalValue}
              portfolioName={activePortfolio.name}
              dayGain={portfolioData.dayGain}
              dayGainPercent={portfolioData.dayGainPercent}
              totalGain={portfolioData.totalGain}
              totalGainPercent={portfolioData.totalGainPercent}
              gainType={gainType}
            />
          )}
        </div>

        {/* Right Column - Portfolio Highlights (Takes 1/3 of space on large screens) */}
        <div className="bg-white dark:bg-gray-900 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-800 dark:text-white">
              Portfolio highlights
            </h2>
          </div>

          {loading ? (
            <div className="text-center py-8">
              <div className="inline-block h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              <p className="mt-2 text-gray-600 dark:text-gray-300">
                Loading portfolio data...
              </p>
            </div>
          ) : error ? null : holdingsData?.holdings?.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500 dark:text-gray-400">
                No investments in this portfolio yet
              </p>
            </div>
          ) : (
            <div className="p-4">
              {/* Side by side DAY GAIN and TOTAL GAIN with colored backgrounds */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                {/* Day Gain */}
                <div
                  className={`${
                    portfolioData.dayGain >= 0
                      ? "bg-green-50 dark:bg-green-900/20"
                      : "bg-red-50 dark:bg-red-900/20"
                  } p-3 rounded-lg`}
                >
                  <div className="text-sm text-gray-500 dark:text-gray-400 uppercase font-medium mb-1">
                    DAY GAIN
                  </div>
                  <div
                    className={`text-lg font-semibold ${
                      portfolioData.dayGain >= 0
                        ? "text-green-500 dark:text-green-400"
                        : "text-red-500 dark:text-red-400"
                    }`}
                  >
                    {portfolioData.dayGain >= 0 ? "+" : ""}
                    {portfolioData.dayGain !== null
                      ? "SAR " + portfolioData.dayGain.toFixed(2)
                      : "SAR 0.00"}
                  </div>
                  <div
                    className={`flex items-center ${
                      portfolioData.dayGainPercent >= 0
                        ? "text-green-500 dark:text-green-400"
                        : "text-red-500 dark:text-red-400"
                    }`}
                  >
                    {portfolioData.dayGainPercent >= 0 ? "▲" : "▼"}{" "}
                    {Math.abs(portfolioData.dayGainPercent || 0)?.toFixed(2)}%
                  </div>
                </div>

                {/* Total Gain */}
                <div
                  className={`${
                    portfolioData.totalGain >= 0
                      ? "bg-green-50 dark:bg-green-900/20"
                      : "bg-red-50 dark:bg-red-900/20"
                  } p-3 rounded-lg`}
                >
                  <div className="text-sm text-gray-500 dark:text-gray-400 uppercase font-medium mb-1">
                    TOTAL GAIN
                  </div>
                  <div
                    className={`text-lg font-semibold ${
                      portfolioData.totalGain >= 0
                        ? "text-green-500 dark:text-green-400"
                        : "text-red-500 dark:text-red-400"
                    }`}
                  >
                    {portfolioData.totalGain >= 0 ? "+" : ""}
                    {portfolioData.totalGain !== null
                      ? "SAR " + portfolioData.totalGain.toFixed(2)
                      : "SAR 0.00"}
                  </div>
                  <div
                    className={`flex items-center ${
                      portfolioData.totalGainPercent >= 0
                        ? "text-green-500 dark:text-green-400"
                        : "text-red-500 dark:text-red-400"
                    }`}
                  >
                    {portfolioData.totalGainPercent >= 0 ? "▲" : "▼"}{" "}
                    {Math.abs(portfolioData.totalGainPercent || 0).toFixed(2)}%
                  </div>
                </div>
              </div>

              {/* Asset Allocation */}
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                <span className="inline-flex items-center justify-center w-5 h-5 bg-gray-200 dark:bg-gray-700 rounded-full mr-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-3 w-3"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z" />
                    <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z" />
                  </svg>
                </span>
                100% stocks
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Holdings Table */}
      {holdingsData &&
      holdingsData.holdings &&
      holdingsData.holdings.length > 0 ? (
        <HoldingsTable
          holdings={holdingsData.holdings}
          portfolioName={activePortfolio.name}
          onRenamePortfolio={handleRenamePortfolio}
          onDeletePortfolio={handleDeletePortfolio}
          onDeleteStock={handleDeleteStock}
          loading={loading}
          gainType={gainType}
          portfolioId={id}
          onGainTypeChange={handleGainTypeChange}
          onOpenAddInvestmentModal={(symbol) => {
            console.log("Opening AddInvestmentModal with symbol:", symbol);
            // Find the stock in the holdings data
            const stock = holdingsData.holdings.find(
              (h) => h.symbol === symbol
            );
            if (stock) {
              // Set the selected stock for the modal with complete data
              setSelectedStock({
                symbol: stock.symbol,
                name: stock.name || stock.symbol,
                currentPrice: stock.current_price,
                price: stock.current_price,
                day_gain_percent: stock.day_gain_percent,
                day_gain: stock.day_gain,
                change: stock.change,
              });
            }
            // Open the modal
            setIsAddModalOpen(true);
          }}
        />
      ) : (
        !loading && (
          <div className="text-center py-10 bg-gray-50 dark:bg-gray-800 rounded-lg shadow">
            <p className="text-gray-500 dark:text-gray-400">
              Nothing in this portfolio yet
            </p>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              Add investments to see performance and track returns
            </p>
            <button
              onClick={() => setIsAddModalOpen(true)}
              className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
            >
              + Add Investment
            </button>
          </div>
        )
      )}

      {/* Add Investment Modal */}
      <AddInvestmentModal
        isOpen={isAddModalOpen}
        onClose={() => {
          setIsAddModalOpen(false);
          setSelectedStock(null); // Reset selected stock when closing modal
          // Refresh holdings data after adding investment
          dispatch(fetchPortfolioHoldings({ portfolioId: id, gainType }));
        }}
        portfolioId={activePortfolio?.id}
        selectedStock={selectedStock} // Pass the selected stock to the modal
      />

      {/* Portfolio Modal */}
      <PortfolioModal
        isOpen={isPortfolioModalOpen}
        onClose={() => setIsPortfolioModalOpen(false)}
      />
    </div>
  );
};

// Portfolio Performance Chart Component
const PortfolioPerformanceChart = ({
  totalValue,
  portfolioName,
  dayGain,
  dayGainPercent,
  totalGain,
  totalGainPercent,
  gainType,
}) => {
  const [timePeriod, setTimePeriod] = useState("1M");

  // Generate mock data based on the selected time period
  const generateMockData = (baseValue, period) => {
    let dataPoints;
    let variance;

    // Set data points and variance based on time period
    switch (period) {
      case "1D":
        dataPoints = 8; // 8 hours in a trading day
        variance = 0.02;
        break;
      case "5D":
        dataPoints = 5; // 5 days
        variance = 0.05;
        break;
      case "1M":
        dataPoints = 30; // 30 days
        variance = 0.1;
        break;
      case "6M":
        dataPoints = 6; // 6 months
        variance = 0.2;
        break;
      case "1Y":
        dataPoints = 12; // 12 months
        variance = 0.3;
        break;
      case "5Y":
        dataPoints = 5; // 5 years
        variance = 0.5;
        break;
      case "MAX":
        dataPoints = 10; // 10 years
        variance = 0.7;
        break;
      default:
        dataPoints = 30;
        variance = 0.1;
    }

    const data = [];
    const labels = [];
    let currentValue = baseValue;

    // Generate data with a slight downward trend and then up at the end
    for (let i = 0; i < dataPoints; i++) {
      const change = Math.random() * variance * 0.5 - variance * 0.7; // Bias toward negative
      currentValue = currentValue * (1 + change);
      data.push(currentValue);

      // Create appropriate labels based on time period
      const date = new Date();
      if (period === "1D") {
        // Hourly labels for 1D
        date.setHours(date.getHours() - (dataPoints - i));
        labels.push(
          date.toLocaleTimeString("en-US", {
            hour: "2-digit",
            minute: "2-digit",
          })
        );
      } else if (period === "5D") {
        // Daily labels for 5D
        date.setDate(date.getDate() - (dataPoints - i));
        labels.push(date.toLocaleDateString("en-US", { weekday: "short" }));
      } else if (period === "1M") {
        // Show dates for 1M
        date.setDate(date.getDate() - (dataPoints - i));
        labels.push(date.toLocaleDateString("en-US", { day: "numeric" }));
      } else if (period === "6M" || period === "1Y") {
        // Monthly labels for 6M and 1Y
        date.setMonth(date.getMonth() - (dataPoints - i));
        labels.push(date.toLocaleDateString("en-US", { month: "short" }));
      } else {
        // Yearly or custom labels for 5Y and MAX
        date.setFullYear(date.getFullYear() - (dataPoints - i));
        labels.push(date.getFullYear().toString());
      }
    }

    // Make sure the last value is exactly the current total value
    data[data.length - 1] = baseValue;

    return { data, labels };
  };

  const { data, labels } = useMemo(
    () => generateMockData(totalValue, timePeriod),
    [totalValue, timePeriod]
  );

  // Use the appropriate gain values based on the selected gain type
  const priceChange = gainType === "dayGain" ? dayGain : totalGain;
  const priceChangePercent =
    gainType === "dayGain" ? dayGainPercent : totalGainPercent;

  // Format the current date and time
  const currentDate = new Date();
  const formattedDate = currentDate.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
  const formattedTime = currentDate.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
  });

  // Chart.js data configuration
  const chartData = {
    labels,
    datasets: [
      {
        label: "Portfolio Value",
        data,
        borderColor: "#3b82f6", // blue-500
        backgroundColor: "rgba(59, 130, 246, 0.05)", // very light blue with opacity
        borderWidth: 2,
        pointRadius: 0,
        pointHoverRadius: 4,
        pointHoverBackgroundColor: "#3b82f6",
        pointHoverBorderColor: "#ffffff",
        tension: 0.4,
        fill: true,
      },
    ],
  };

  // Chart.js options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        mode: "index",
        intersect: false,
        backgroundColor: "rgba(17, 24, 39, 0.9)", // dark background
        titleColor: "#ffffff",
        bodyColor: "#ffffff",
        borderColor: "rgba(75, 85, 99, 0.3)",
        borderWidth: 1,
        padding: 10,
        callbacks: {
          label: function (context) {
            let label = context.dataset.label || "";
            if (label) {
              label += ": ";
            }
            if (context.parsed.y !== null) {
              label += new Intl.NumberFormat("en-SA", {
                style: "currency",
                currency: "SAR",
                minimumFractionDigits: 2,
              }).format(context.parsed.y);
            }
            return label;
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: true,
          color: (context) => {
            const chart = context.chart;
            const { ctx, chartArea } = chart;
            if (!chartArea) return "rgba(75, 85, 99, 0.15)";
            // Check if we're in dark mode by looking at the computed background color
            const computedStyle = getComputedStyle(chart.canvas.parentNode);
            const backgroundColor = computedStyle.backgroundColor;
            const isDarkMode =
              backgroundColor.includes("rgb(15, 23, 42)") ||
              backgroundColor.includes("rgba(15, 23, 42");
            return isDarkMode
              ? "rgba(75, 85, 99, 0.15)"
              : "rgba(75, 85, 99, 0.1)";
          },
          drawBorder: false,
          z: -1,
        },
        ticks: {
          color: (context) => {
            const chart = context.chart;
            const { ctx, chartArea } = chart;
            if (!chartArea) return "rgba(156, 163, 175, 0.7)";
            const computedStyle = getComputedStyle(chart.canvas.parentNode);
            const backgroundColor = computedStyle.backgroundColor;
            const isDarkMode =
              backgroundColor.includes("rgb(15, 23, 42)") ||
              backgroundColor.includes("rgba(15, 23, 42");
            return isDarkMode
              ? "rgba(156, 163, 175, 0.7)"
              : "rgba(75, 85, 99, 0.7)";
          },
          font: {
            size: 10,
          },
          maxRotation: 0,
          maxTicksLimit: 15,
        },
      },
      y: {
        position: "right",
        grid: {
          color: (context) => {
            const chart = context.chart;
            const { ctx, chartArea } = chart;
            if (!chartArea) return "rgba(75, 85, 99, 0.15)";
            const computedStyle = getComputedStyle(chart.canvas.parentNode);
            const backgroundColor = computedStyle.backgroundColor;
            const isDarkMode =
              backgroundColor.includes("rgb(15, 23, 42)") ||
              backgroundColor.includes("rgba(15, 23, 42");
            return isDarkMode
              ? "rgba(75, 85, 99, 0.15)"
              : "rgba(75, 85, 99, 0.1)";
          },
          drawBorder: false,
          z: -1,
        },
        ticks: {
          color: (context) => {
            const chart = context.chart;
            const { ctx, chartArea } = chart;
            if (!chartArea) return "rgba(156, 163, 175, 0.7)";
            const computedStyle = getComputedStyle(chart.canvas.parentNode);
            const backgroundColor = computedStyle.backgroundColor;
            const isDarkMode =
              backgroundColor.includes("rgb(15, 23, 42)") ||
              backgroundColor.includes("rgba(15, 23, 42");
            return isDarkMode
              ? "rgba(156, 163, 175, 0.7)"
              : "rgba(75, 85, 99, 0.7)";
          },
          font: {
            size: 10,
          },
          callback: function (value) {
            return "SAR " + Math.round(value);
          },
          padding: 10,
          stepSize: 20,
        },
      },
    },
    interaction: {
      mode: "nearest",
      axis: "x",
      intersect: false,
    },
  };

  return (
    <div className="chart-container bg-white dark:bg-[#0f172a] text-gray-800 dark:text-white p-4 rounded-lg shadow border border-gray-200 dark:border-gray-700">
      {/* Portfolio Value and Change */}
      <div className="mb-4">
        <div className="flex justify-between items-center">
          <div className="text-3xl font-semibold">
            SAR {totalValue.toFixed(2)}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() =>
                window.dispatchEvent(
                  new CustomEvent("gainTypeChange", { detail: "dayGain" })
                )
              }
              className={`px-3 py-1 text-sm rounded-full ${
                gainType === "dayGain"
                  ? "bg-blue-500 text-white"
                  : "bg-gray-100 dark:bg-[#1e293b] text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
              }`}
            >
              Day Gain
            </button>
            <button
              onClick={() =>
                window.dispatchEvent(
                  new CustomEvent("gainTypeChange", { detail: "totalGain" })
                )
              }
              className={`px-3 py-1 text-sm rounded-full ${
                gainType === "totalGain"
                  ? "bg-blue-500 text-white"
                  : "bg-gray-100 dark:bg-[#1e293b] text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
              }`}
            >
              Total Gain
            </button>
          </div>
        </div>
        <div className="flex items-center text-sm mt-2">
          <span
            className={`${
              priceChange >= 0
                ? "text-green-500 dark:text-green-400"
                : "text-red-500 dark:text-red-400"
            } font-medium`}
          >
            {priceChange >= 0 ? "+" : ""}
            {priceChange !== 0
              ? "SAR " + Math.abs(priceChange).toFixed(2)
              : "SAR 0"}
          </span>
          <span
            className={`flex items-center ${
              priceChangePercent >= 0
                ? "text-green-500 dark:text-green-400"
                : "text-red-500 dark:text-red-400"
            } font-medium ml-2`}
          >
            {priceChangePercent >= 0 ? "▲" : "▼"}{" "}
            {Math.abs(priceChangePercent || 0).toFixed(2)}%
          </span>
          <span className="text-gray-500 dark:text-gray-400 ml-2">
            {timePeriod}
          </span>
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          {formattedDate.replace(",", "")}, {formattedTime} UTC+5:30 ·{" "}
          <span className="text-gray-600 dark:text-gray-500">Disclaimer</span>
        </div>
      </div>

      {/* Time Period Selector */}
      <div className="flex space-x-1 mb-6 overflow-x-auto">
        {["1D", "5D", "1M", "6M", "1Y", "5Y", "MAX"].map((period) => (
          <button
            key={period}
            onClick={() => setTimePeriod(period)}
            className={`px-3 py-1 text-sm rounded-full ${
              timePeriod === period
                ? "bg-blue-500 text-white"
                : "bg-gray-100 dark:bg-[#1e293b] text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
            }`}
          >
            {period}
          </button>
        ))}
      </div>

      {/* Chart */}
      <div className="h-64">
        <Line data={chartData} options={chartOptions} />
      </div>
    </div>
  );
};

export default PortfolioDetails;
